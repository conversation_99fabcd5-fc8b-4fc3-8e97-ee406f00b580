import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Calendar,
  MapPin,
  Award,
  Users,
  Info,
  Phone,
  Mail,
  Star,
  Shield,
  Wrench,
  Utensils,
  Hotel,
  Package,
  Globe,
  Target,
  Heart,
} from "lucide-react"

export default function GranFondoPage() {
  return (
    <div className="flex flex-col min-h-[100dvh] bg-light-gray text-brand-charcoal">
      <header className="fixed top-0 left-0 right-0 z-50 bg-brand-white/90 backdrop-blur-sm shadow-lg border-b border-light-gray">
        <div className="container mx-auto flex h-20 items-center justify-between px-4 md:px-6">
          <Link href="#accueil" className="flex items-center gap-3 group">
            <div className="w-12 h-12 bg-gradient-to-br from-brand-blue to-sky-gradient rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <Image
                src="/logo.png"
                alt="Logo Gran Fondo Îles de Guadeloupe"
                width={24}
                height={24}
                className="invert"
              />
            </div>
            <span className="font-bold text-lg sm:text-xl text-brand-blue whitespace-nowrap">
              Gran Fondo <span className="hidden sm:inline text-brand-orange">Îles de Guadeloupe</span>
            </span>
          </Link>
          <nav className="hidden lg:flex items-center gap-8 text-sm font-medium">
            <Link href="#epreuve" className="hover:text-brand-orange transition-colors duration-300 relative group">
              L'Épreuve
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="#programme" className="hover:text-brand-orange transition-colors duration-300 relative group">
              Programme
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link
              href="#participation"
              className="hover:text-brand-orange transition-colors duration-300 relative group"
            >
              Participation
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link
              href="#infos-pratiques"
              className="hover:text-brand-orange transition-colors duration-300 relative group"
            >
              Infos Pratiques
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="#partenaires" className="hover:text-brand-orange transition-colors duration-300 relative group">
              Partenaires
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="#contact" className="hover:text-brand-orange transition-colors duration-300 relative group">
              Contact
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-brand-orange transition-all duration-300 group-hover:w-full"></span>
            </Link>
          </nav>
          <Button
            asChild
            className="hidden lg:flex bg-gradient-to-r from-brand-orange to-orange-500 hover:from-orange-500 hover:to-brand-orange text-white shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <a
              href="https://www.sportsnconnect.com/calendrier-evenements/view/29239/gran-fondo-guadeloupe"
              target="_blank"
              rel="noopener noreferrer"
            >
              S'inscrire
            </a>
          </Button>
        </div>
      </header>

      <main className="flex-1">
        <section
          id="accueil"
          className="relative w-full pt-20 lg:pt-28 pb-16 md:pb-24 bg-gradient-to-br from-brand-blue via-sky-gradient to-brand-sky text-white overflow-hidden"
          style={{
            backgroundImage: `url('/images/topographic-pattern.svg')`,
            backgroundRepeat: "repeat",
            backgroundSize: "400px",
            backgroundOpacity: "0.1",
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-brand-blue/80 to-transparent"></div>
          <div className="container mx-auto px-4 md:px-6 grid lg:grid-cols-2 gap-8 items-center relative z-10">
            <div className="flex flex-col gap-6 text-center lg:text-left">
              <div className="inline-flex items-center gap-2 bg-brand-orange/20 backdrop-blur-sm px-4 py-2 rounded-full text-brand-orange font-semibold text-sm w-fit mx-auto lg:mx-0">
                <Star className="w-4 h-4" />
                Événement UCI Officiel
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight leading-tight">
                Cap sur la Guadeloupe pour une aventure sportive{" "}
                <span className="text-brand-orange italic bg-gradient-to-r from-brand-orange to-yellow-400 bg-clip-text text-transparent">
                  unique !
                </span>
              </h1>
              <p className="text-lg md:text-xl text-white/90 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                Rejoignez l'élite du cyclisme amateur pour une épreuve inoubliable au cœur des Antilles françaises.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 mt-4">
                <div className="flex items-center gap-3 bg-white/20 backdrop-blur-sm px-6 py-3 rounded-2xl border border-white/30 hover:bg-white/30 transition-all duration-300">
                  <div className="w-10 h-10 bg-brand-orange rounded-full flex items-center justify-center">
                    <Calendar className="w-5 h-5 text-white" />
                  </div>
                  <span className="font-semibold">6–7 décembre 2025</span>
                </div>
                <div className="flex items-center gap-3 bg-white/20 backdrop-blur-sm px-6 py-3 rounded-2xl border border-white/30 hover:bg-white/30 transition-all duration-300">
                  <div className="w-10 h-10 bg-palm-green rounded-full flex items-center justify-center">
                    <MapPin className="w-5 h-5 text-white" />
                  </div>
                  <span className="font-semibold">Guadeloupe</span>
                </div>
              </div>
              <div className="mt-8 flex justify-center lg:justify-start">
                <Button
                  size="lg"
                  asChild
                  className="bg-gradient-to-r from-brand-orange to-orange-500 hover:from-orange-500 hover:to-brand-orange text-white text-lg font-bold px-10 py-6 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 hover:scale-105"
                >
                  <a
                    href="https://www.sportsnconnect.com/calendrier-evenements/view/29239/gran-fondo-guadeloupe"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Je m'inscris maintenant
                  </a>
                </Button>
              </div>
            </div>
            <div className="relative h-64 sm:h-80 lg:h-full w-full bg-transparent shadow-none">
              <div className="absolute inset-0 from-brand-blue/20 to-transparent rounded-3xl"></div>
              <Image
                src="/images/home-hero.png"
                alt="Cyclistes participant au Gran Fondo en Guadeloupe"
                fill
                className="object-contain drop-shadow-2xl"
              />
            </div>
          </div>
        </section>

        <section id="epreuve" className="py-16 md:py-24 bg-gradient-to-br from-brand-white to-light-gray">
          <div className="container mx-auto px-4 md:px-6 grid lg:grid-cols-2 gap-12 items-center">
            <div className="order-2 lg:order-1">
              <div className="inline-flex items-center gap-2 bg-brand-sky/10 px-4 py-2 rounded-full text-brand-sky font-semibold text-sm mb-6">
                <Globe className="w-4 h-4" />
                Série Mondiale UCI
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-6 leading-tight">
                L'Épreuve : UCI Gran Fondo World Series
              </h2>
              <div className="space-y-6">
                <Card className="bg-gradient-to-r from-brand-white to-sky-gradient/5 border-l-4 border-brand-sky shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-brand-sky to-sky-gradient rounded-xl flex items-center justify-center flex-shrink-0">
                        <Target className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-lg text-brand-blue mb-2">Qualification Mondiale</h3>
                        <p className="text-dark-gray leading-relaxed">
                          L'UCI Gran Fondo World Series est une série de compétitions internationales de cyclisme
                          amateur. 25% des meilleurs participants de chaque tranche d'âge sont qualifiés pour le
                          Championnat du Monde 2026 au Japon.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-gradient-to-r from-brand-white to-palm-green/5 border-l-4 border-palm-green shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-palm-green to-mountain-green rounded-xl flex items-center justify-center flex-shrink-0">
                        <Heart className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-lg text-brand-blue mb-2">Cyclisme pour Tous</h3>
                        <p className="text-dark-gray leading-relaxed">
                          Le Gran Fondo Îles de Guadeloupe valorise l'inclusivité, la performance et l'ouverture à tous
                          les niveaux, dans un cadre sécurisé et professionnel. Une occasion unique de découvrir les
                          paysages époustouflants de la Guadeloupe.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-brand-sky/20 to-palm-green/20 rounded-3xl transform rotate-3"></div>
                <Image
                  src="/guadeloupe-coastline.png"
                  alt="Paysage côtier de la Guadeloupe"
                  width={600}
                  height={400}
                  className="rounded-3xl shadow-2xl w-full relative z-10 hover:scale-105 transition-transform duration-500"
                />
              </div>
            </div>
          </div>
        </section>

        <section id="programme" className="py-16 md:py-24 bg-gradient-to-br from-light-gray to-brand-white">
          <div className="container mx-auto px-4 md:px-6">
            <div className="text-center mb-12">
              <div className="inline-flex items-center gap-2 bg-brand-orange/10 px-4 py-2 rounded-full text-brand-orange font-semibold text-sm mb-4">
                <Calendar className="w-4 h-4" />
                Programme 2025
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-4">Programme de l'événement</h2>
              <p className="text-lg text-dark-gray">Deux jours de cyclisme et de découverte.</p>
            </div>
            <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
              <Card className="bg-gradient-to-br from-brand-white to-brand-sky/10 shadow-2xl border-0 hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 group overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-brand-blue to-brand-sky"></div>
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center justify-between text-2xl font-bold">
                    <span className="text-brand-blue">Jour 1 : Samedi 6 déc.</span>
                    <div className="w-16 h-16 bg-gradient-to-br from-brand-blue to-brand-sky text-white rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Calendar className="w-8 h-8" />
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-brand-sky/10 p-4 rounded-xl">
                    <h3 className="font-bold text-lg text-brand-blue">Rallye Découverte (non compétitif)</h3>
                    <p className="text-dark-gray">Distance : 80 km</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="font-semibold text-brand-blue">Départ :</p>
                      <p className="text-dark-gray">10h00</p>
                    </div>
                    <div>
                      <p className="font-semibold text-brand-blue">Arrivée estimée :</p>
                      <p className="text-dark-gray">14h00</p>
                    </div>
                  </div>
                  <div className="bg-palm-green/10 p-4 rounded-xl">
                    <p className="font-semibold text-brand-blue">Lieu :</p>
                    <p className="text-dark-gray">Plage Raisin Claire, Saint-François</p>
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-gradient-to-br from-brand-white to-brand-orange/10 shadow-2xl border-0 hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 group overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-brand-orange to-orange-500"></div>
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center justify-between text-2xl font-bold">
                    <span className="text-brand-blue">Jour 2 : Dimanche 7 déc.</span>
                    <div className="w-16 h-16 bg-gradient-to-br from-brand-orange to-orange-500 text-white rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Award className="w-8 h-8" />
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-brand-orange/10 p-4 rounded-xl">
                    <h3 className="font-bold text-lg text-brand-blue">Gran Fondo (qualificative)</h3>
                    <p className="text-dark-gray">Distance : 124 km / Dénivelé : 2 883 m</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="font-semibold text-brand-blue">Départ :</p>
                      <p className="text-dark-gray">9h00</p>
                    </div>
                    <div>
                      <p className="font-semibold text-brand-blue">Arrivée estimée :</p>
                      <p className="text-dark-gray">14h00</p>
                    </div>
                  </div>
                  <div className="bg-mountain-green/10 p-4 rounded-xl">
                    <p className="font-semibold text-brand-blue">Lieu de départ/arrivée :</p>
                    <p className="text-dark-gray">Capesterre-Belle-Eau</p>
                  </div>
                  <div className="bg-palm-green/10 p-4 rounded-xl">
                    <p className="font-semibold text-brand-blue">Point clé :</p>
                    <p className="text-dark-gray">Montée du Col des Mamelles (13,5 km total, dont 4 km à 8.5%)</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        <section id="participation" className="py-16 md:py-24 bg-gradient-to-br from-brand-white to-light-gray">
          <div className="container mx-auto px-4 md:px-6">
            <div className="text-center mb-12">
              <div className="inline-flex items-center gap-2 bg-brand-blue/10 px-4 py-2 rounded-full text-brand-blue font-semibold text-sm mb-4">
                <Users className="w-4 h-4" />
                Inscription Ouverte
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-4">Conditions de Participation</h2>
              <p className="text-lg text-dark-gray">Qui peut participer à l'événement ?</p>
            </div>
            <div className="grid lg:grid-cols-3 gap-8">
              <Card className="bg-gradient-to-br from-brand-white to-brand-sky/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group">
                <CardHeader className="pb-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-brand-blue to-brand-sky rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Users className="text-white w-8 h-8" />
                  </div>
                  <CardTitle className="text-xl font-bold text-brand-blue">Conditions</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 text-dark-gray">
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
                      <span>Ouvert à tous les cyclistes amateurs à partir de 19 ans.</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
                      <span>Licences acceptées : amateure, master, élite, cyclisme pour tous.</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
                      <span>Non licenciés acceptés avec certificat médical ou licence d'un jour.</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-brand-white to-brand-orange/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group">
                <CardHeader className="pb-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-brand-orange to-orange-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Star className="text-white w-8 h-8" />
                  </div>
                  <CardTitle className="text-xl font-bold text-brand-blue">Catégories d'âge</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2 text-dark-gray">
                    {["19–34", "35–39", "40–44", "45–49", "50–54", "55–59", "60–64", "65–69", "70–74", "75–79 ans"].map(
                      (age, index) => (
                        <div key={index} className="flex items-center gap-2 py-1">
                          <div className="w-2 h-2 bg-brand-orange rounded-full flex-shrink-0"></div>
                          <span className="text-sm font-medium">{age}</span>
                        </div>
                      ),
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-brand-white to-red-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group">
                <CardHeader className="pb-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Info className="text-white w-8 h-8" />
                  </div>
                  <CardTitle className="text-xl font-bold text-brand-blue">Restrictions</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 text-dark-gray">
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
                      <span>Membres d'équipes UCI de l'année en cours interdits.</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
                      <span>Participants à des épreuves UCI internationales la même année interdits.</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-brand-orange rounded-full mt-2 flex-shrink-0"></div>
                      <span>Cyclistes avec points UCI durant la saison précédente non éligibles.</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
            <div className="text-center mt-12">
              <Button
                size="lg"
                asChild
                className="bg-gradient-to-r from-brand-blue to-brand-sky hover:from-brand-sky hover:to-brand-blue text-white font-bold shadow-lg hover:shadow-xl transition-all duration-300 px-8 py-6 rounded-2xl"
              >
                <a
                  href="https://www.sportsnconnect.com/calendrier-evenements/view/29239/gran-fondo-guadeloupe"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Inscription en ligne (jusqu'au 30/11/2025)
                </a>
              </Button>
            </div>
          </div>
        </section>

        <section id="infos-pratiques" className="py-16 md:py-24 bg-gradient-to-br from-light-gray to-brand-white">
          <div className="container mx-auto px-4 md:px-6">
            <div className="text-center mb-12">
              <div className="inline-flex items-center gap-2 bg-palm-green/10 px-4 py-2 rounded-full text-palm-green font-semibold text-sm mb-4">
                <Info className="w-4 h-4" />
                Informations Essentielles
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-4">Informations Pratiques</h2>
              <p className="text-lg text-dark-gray">Tout ce que vous devez savoir pour préparer votre course.</p>
            </div>
            <div className="grid lg:grid-cols-3 gap-8">
              <Card className="bg-gradient-to-br from-brand-white to-palm-green/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group">
                <CardHeader className="pb-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-palm-green to-mountain-green rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Package className="text-white w-8 h-8" />
                  </div>
                  <CardTitle className="text-xl font-bold text-brand-blue">Inclus dans l'inscription</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-dark-gray">
                  <p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
                    <Award className="w-5 h-5 text-brand-orange flex-shrink-0" />
                    <span>Dossard & Chronométrage</span>
                  </p>
                  <p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
                    <Shield className="w-5 h-5 text-brand-orange flex-shrink-0" />
                    <span>Piste sécurisée & Service médical</span>
                  </p>
                  <p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
                    <Utensils className="w-5 h-5 text-brand-orange flex-shrink-0" />
                    <span>Ravitaillement</span>
                  </p>
                  <p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
                    <Wrench className="w-5 h-5 text-brand-orange flex-shrink-0" />
                    <span>Assistance technique</span>
                  </p>
                  <p className="flex items-center gap-3 p-2 bg-brand-white/50 rounded-lg">
                    <Star className="w-5 h-5 text-brand-orange flex-shrink-0" />
                    <span>Médaille pour tous & récompenses</span>
                  </p>
                </CardContent>
              </Card>
              <Card className="bg-gradient-to-br from-brand-white to-brand-sky/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group">
                <CardHeader className="pb-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-brand-sky to-sky-gradient rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Hotel className="text-white w-8 h-8" />
                  </div>
                  <CardTitle className="text-xl font-bold text-brand-blue">Hébergement & Transport</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 text-dark-gray">
                  <div className="p-4 bg-brand-sky/10 rounded-xl">
                    <p className="font-semibold text-brand-blue mb-1">Kit coureur & expo :</p>
                    <p>4 déc. au Mémorial ACTe, Pointe-à-Pitre.</p>
                  </div>
                  <div className="p-4 bg-palm-green/10 rounded-xl">
                    <p className="font-semibold text-brand-blue mb-1">Hébergement :</p>
                    <p>86 hôtels et 36 000 solutions alternatives (Airbnb, gîtes...).</p>
                  </div>
                  <div className="p-4 bg-brand-orange/10 rounded-xl">
                    <p className="font-semibold text-brand-blue mb-1">Transport :</p>
                    <p>
                      Aéroport international Guadeloupe Pôle Caraïbes avec liaisons directes depuis l'Europe et
                      l'Amérique du Nord.
                    </p>
                  </div>
                </CardContent>
              </Card>
              <div className="lg:col-span-1">
                <div className="relative h-full min-h-[400px]">
                  <div className="absolute inset-0 bg-gradient-to-br from-brand-orange/20 to-palm-green/20 rounded-3xl transform rotate-2"></div>
                  <Image
                    src="/guadeloupe-hotel-resort.png"
                    alt="Hôtel en Guadeloupe"
                    width={400}
                    height={400}
                    className="rounded-3xl shadow-2xl w-full h-full object-cover relative z-10 hover:scale-105 transition-transform duration-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        <section id="partenaires" className="py-16 md:py-24 bg-gradient-to-br from-brand-white to-light-gray">
          <div className="container mx-auto px-4 md:px-6 text-center">
            <div className="inline-flex items-center gap-2 bg-brand-orange/10 px-4 py-2 rounded-full text-brand-orange font-semibold text-sm mb-4">
              <Star className="w-4 h-4" />
              Partenaires Officiels
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-4">Nos Partenaires & Organisateurs</h2>
            <p className="text-lg text-dark-gray mb-12">Un grand merci à ceux qui rendent cet événement possible.</p>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 items-center">
              <Card className="bg-gradient-to-br from-brand-white to-brand-blue/5 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-brand-blue to-brand-sky rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Image src="/odass-logo.png" alt="Logo OD'ASS" width={40} height={40} className="invert" />
                  </div>
                  <p className="font-bold text-brand-blue text-sm">OD'ASS</p>
                  <p className="text-xs text-dark-gray">(Organisateur)</p>
                </div>
              </Card>
              <Card className="bg-gradient-to-br from-brand-white to-palm-green/5 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-palm-green to-mountain-green rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Image
                      src="/team-cama-ccd-logo.png"
                      alt="Logo Team Cama CCD"
                      width={40}
                      height={40}
                      className="invert"
                    />
                  </div>
                  <p className="font-bold text-brand-blue text-sm">Team Cama CCD</p>
                </div>
              </Card>
              <Card className="bg-gradient-to-br from-brand-white to-brand-orange/5 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-brand-orange to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Image
                      src="/golazo-sports-logo.png"
                      alt="Logo Golazo Sports"
                      width={40}
                      height={40}
                      className="invert"
                    />
                  </div>
                  <p className="font-bold text-brand-blue text-sm">Golazo Sports</p>
                </div>
              </Card>
              <Card className="bg-gradient-to-br from-brand-white to-brand-sky/5 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-brand-sky to-sky-gradient rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Image
                      src="/placeholder.svg?height=40&width=40"
                      alt="Logo France Télévisions"
                      width={40}
                      height={40}
                      className="invert"
                    />
                  </div>
                  <p className="font-bold text-brand-blue text-sm">France Télévisions</p>
                </div>
              </Card>
              <Card className="bg-gradient-to-br from-brand-white to-red-50 p-6 border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Image
                      src="/placeholder.svg?height=40&width=40"
                      alt="Logo Eurosport"
                      width={40}
                      height={40}
                      className="invert"
                    />
                  </div>
                  <p className="font-bold text-brand-blue text-sm">Eurosport</p>
                </div>
              </Card>
            </div>
          </div>
        </section>

        <section id="contact" className="py-16 md:py-24 bg-gradient-to-br from-light-gray to-brand-white">
          <div className="container mx-auto px-4 md:px-6">
            <div className="text-center mb-12">
              <div className="inline-flex items-center gap-2 bg-brand-blue/10 px-4 py-2 rounded-full text-brand-blue font-semibold text-sm mb-4">
                <Phone className="w-4 h-4" />
                Nous Contacter
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-brand-blue mb-4">Contactez-nous</h2>
              <p className="text-lg text-dark-gray">Une question ? N'hésitez pas à nous joindre.</p>
            </div>
            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <Card className="bg-gradient-to-br from-brand-white to-palm-green/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-palm-green to-mountain-green rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <MapPin className="text-white w-8 h-8" />
                    </div>
                    <div>
                      <CardTitle className="text-brand-blue">Contact Guadeloupe</CardTitle>
                      <p className="text-dark-gray">Jacky GIBRIEN</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <a
                    href="tel:+590690996000"
                    className="flex items-center gap-3 p-3 bg-brand-white/50 rounded-xl hover:bg-palm-green/10 transition-colors duration-300 group"
                  >
                    <Phone className="w-5 h-5 text-palm-green" />
                    <span className="group-hover:text-palm-green transition-colors">+590 690 99 60 00</span>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center gap-3 p-3 bg-brand-white/50 rounded-xl hover:bg-palm-green/10 transition-colors duration-300 group"
                  >
                    <Mail className="w-5 h-5 text-palm-green" />
                    <span className="group-hover:text-palm-green transition-colors"><EMAIL></span>
                  </a>
                </CardContent>
              </Card>
              <Card className="bg-gradient-to-br from-brand-white to-brand-sky/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 group">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-brand-sky to-sky-gradient rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <MapPin className="text-white w-8 h-8" />
                    </div>
                    <div>
                      <CardTitle className="text-brand-blue">Contact France hexagonale</CardTitle>
                      <p className="text-dark-gray">Francis GIBRIEN</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <a
                    href="tel:+33681444406"
                    className="flex items-center gap-3 p-3 bg-brand-white/50 rounded-xl hover:bg-brand-sky/10 transition-colors duration-300 group"
                  >
                    <Phone className="w-5 h-5 text-brand-sky" />
                    <span className="group-hover:text-brand-sky transition-colors">+33 6 81 44 44 06</span>
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center gap-3 p-3 bg-brand-white/50 rounded-xl hover:bg-brand-sky/10 transition-colors duration-300 group"
                  >
                    <Mail className="w-5 h-5 text-brand-sky" />
                    <span className="group-hover:text-brand-sky transition-colors"><EMAIL></span>
                  </a>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>

      <footer className="bg-gradient-to-r from-brand-blue via-sky-gradient to-brand-sky text-white">
        <div className="container mx-auto py-8 px-4 md:px-6">
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 bg-brand-orange rounded-xl flex items-center justify-center">
                <Image src="/logo.png" alt="Logo Gran Fondo" width={24} height={24} className="invert" />
              </div>
              <span className="font-bold text-xl">Gran Fondo Îles de Guadeloupe</span>
            </div>
            <p className="text-sm text-white/80">
              &copy; {new Date().getFullYear()} UCI Gran Fondo Îles de Guadeloupe. Tous droits réservés.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
