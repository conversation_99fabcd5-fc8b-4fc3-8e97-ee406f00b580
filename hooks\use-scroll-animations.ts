"use client";

import { useEffect } from "react";

export const useScrollAnimations = () => {
	useEffect(() => {
		// Check if user prefers reduced motion
		const prefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches;

		if (prefersReducedMotion) {
			// If reduced motion is preferred, show all elements immediately
			const allAnimateElements = document.querySelectorAll(
				".animate-on-scroll, .animate-fade-in-up, .animate-slide-in-left, .animate-slide-in-right, .animate-scale-in"
			);
			allAnimateElements.forEach((el) => {
				el.classList.add("animate-in");
			});
			return;
		}

		// Create intersection observer for scroll-triggered animations
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						console.log("Animating element:", entry.target);
						entry.target.classList.add("animate-in");

						// Also trigger animations for child elements with stagger classes
						const staggerElements = entry.target.querySelectorAll('[class*="animate-stagger"]');
						staggerElements.forEach((child) => {
							console.log("Animating stagger child:", child);
							child.classList.add("animate-in");
						});
					}
				});
			},
			{
				threshold: 0.1,
				rootMargin: "-10px 0px -10px 0px",
			}
		);

		// Wait for DOM to be ready
		const initializeAnimations = () => {
			// Observe all elements with animate-on-scroll class
			const animateOnScrollElements = document.querySelectorAll(".animate-on-scroll");
			animateOnScrollElements.forEach((el) => observer.observe(el));

			// Also observe individual animated elements that aren't in animate-on-scroll containers
			const individualElements = document.querySelectorAll(
				".animate-slide-in-left, .animate-slide-in-right, .animate-scale-in:not(.animate-on-scroll *), .animate-fade-in-up:not(.animate-on-scroll *)"
			);
			individualElements.forEach((el) => observer.observe(el));
		};

		// Initialize immediately if DOM is ready, otherwise wait
		if (document.readyState === "loading") {
			document.addEventListener("DOMContentLoaded", initializeAnimations);
		} else {
			initializeAnimations();
		}

		// Cleanup function
		return () => {
			observer.disconnect();
		};
	}, []);
};
